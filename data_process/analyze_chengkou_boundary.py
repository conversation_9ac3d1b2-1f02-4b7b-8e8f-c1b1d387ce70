import xarray as xr
import geopandas as gpd
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from shapely.geometry import Point
import warnings
warnings.filterwarnings('ignore')

def analyze_chengkou_grid_points():
    """
    分析城口流域边界内的栅格点数量并可视化
    """
    print("🗺️  分析城口流域边界内的栅格点...")
    
    # 读取城口.shp边界文件
    print("正在读取城口流域边界文件...")
    shapefile_path = "/home/<USER>/Flood_flow_prediction/boundary/chengkou.shp"
    chengkou_boundary = gpd.read_file(shapefile_path)
    
    # 确保坐标系正确
    if chengkou_boundary.crs is None:
        chengkou_boundary = chengkou_boundary.set_crs("EPSG:4326")
    elif chengkou_boundary.crs.to_string() != "EPSG:4326":
        chengkou_boundary = chengkou_boundary.to_crs("EPSG:4326")
    
    print(f"流域边界坐标系: {chengkou_boundary.crs}")
    print(f"流域边界范围: {chengkou_boundary.bounds}")
    
    try:
        # 加载GFS数据
        print("正在加载GFS数据...")
        ds = xr.open_zarr("https://data.dynamical.org/noaa/gfs/forecast/latest.zarr?email=<EMAIL>")
        
        # 获取全球所有经纬度坐标点
        lons = ds.longitude.values
        lats = ds.latitude.values
        print(f"全球数据大小: 经度 {len(lons)} 点, 纬度 {len(lats)} 点")
        
        # 获取流域边界框，用于初步筛选
        bounds = chengkou_boundary.total_bounds
        min_lon, min_lat, max_lon, max_lat = bounds
        print(f"流域边界框: 经度 [{min_lon:.4f}, {max_lon:.4f}], 纬度 [{min_lat:.4f}, {max_lat:.4f}]")
        
        # 直接使用流域边界框筛选候选点（不扩展）
        lon_mask = (lons >= min_lon) & (lons <= max_lon)
        lat_mask = (lats >= min_lat) & (lats <= max_lat)
        
        candidate_lons = lons[lon_mask]
        candidate_lats = lats[lat_mask]
        
        print(f"流域边界框内的候选点: 经度 {len(candidate_lons)} 点, 纬度 {len(candidate_lats)} 点")
        print(f"总候选点数: {len(candidate_lons) * len(candidate_lats)}")
        
        # 显示具体的候选经纬度值
        print(f"\n候选经度值: {candidate_lons}")
        print(f"候选纬度值: {candidate_lats}")
        
        # 检查GFS数据的分辨率
        lon_resolution = abs(lons[1] - lons[0]) if len(lons) > 1 else 0
        lat_resolution = abs(lats[1] - lats[0]) if len(lats) > 1 else 0
        print(f"\nGFS数据分辨率: 经度 {lon_resolution:.3f}°, 纬度 {lat_resolution:.3f}°")
        
        # 计算理论上应该有多少个点
        lon_span = max_lon - min_lon
        lat_span = max_lat - min_lat
        expected_lon_points = int(lon_span / lon_resolution) + 1 if lon_resolution > 0 else 0
        expected_lat_points = int(lat_span / lat_resolution) + 1 if lat_resolution > 0 else 0
        print(f"流域跨度: 经度 {lon_span:.4f}°, 纬度 {lat_span:.4f}°")
        print(f"流域内理论栅格点数: 经度约 {expected_lon_points} 点, 纬度约 {expected_lat_points} 点")
        
        # 精确检查哪些点落在流域边界内
        print("正在精确检查哪些坐标点落在流域边界内...")
        
        all_points = []
        valid_coordinates = []
        candidate_coordinates = []
        
        for i, lat in enumerate(candidate_lats):
            for j, lon in enumerate(candidate_lons):
                point = Point(lon, lat)
                candidate_coordinates.append((lon, lat))
                
                # 检查点是否在流域边界内
                if chengkou_boundary.geometry.contains(point).any():
                    all_points.append(point)
                    valid_coordinates.append((lon, lat))
        
        print(f"🎯 找到 {len(all_points)} 个坐标点落在城口流域边界内")
        print(f"检查的候选点总数: {len(candidate_coordinates)}")
        
        # 打印所有有效坐标点
        print("\n📍 落在城口流域边界内的坐标点：")
        print("-" * 60)
        for i, (lon, lat) in enumerate(valid_coordinates, 1):
            print(f"点 {i:2d}: (经度: {lon:8.4f}°, 纬度: {lat:7.4f}°)")
        
        # 如果没有点在边界内，选择距离最近的点
        if len(all_points) == 0:
            print("⚠️  警告: 没有找到落在流域边界内的坐标点")
            print("正在寻找距离流域中心最近的点...")
            
            watershed_centroid = chengkou_boundary.geometry.centroid.iloc[0]
            min_distance = float('inf')
            closest_point = None
            
            for lon, lat in candidate_coordinates:
                point = Point(lon, lat)
                distance = point.distance(watershed_centroid)
                if distance < min_distance:
                    min_distance = distance
                    closest_point = (lon, lat)
            
            if closest_point:
                valid_coordinates = [closest_point]
                print(f"选择最近点: 经度 {closest_point[0]:.4f}°, 纬度 {closest_point[1]:.4f}°")
                print(f"距离流域中心: {min_distance:.6f}度")
        
        # 创建可视化图表
        create_visualization(chengkou_boundary, valid_coordinates, candidate_coordinates)
        
        return valid_coordinates, candidate_coordinates
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return [], []

def create_visualization(boundary, valid_coords, candidate_coords):
    """
    创建流域边界和栅格点的可视化图表
    """
    print("\n🎨 正在创建可视化图表...")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))
    
    # 只绘制流域边界（红色边框，无填充）
    boundary.boundary.plot(ax=ax, color='red', linewidth=2, label='城口流域边界')
    
    # 绘制有效栅格点（蓝色填充的方形）
    if valid_coords:
        grid_size = 0.25  # GFS栅格大小（度）
        
        for i, (lon, lat) in enumerate(valid_coords, 1):
            # 计算栅格边界
            half_size = grid_size / 2
            min_lon = lon - half_size
            max_lon = lon + half_size
            min_lat = lat - half_size
            max_lat = lat + half_size
            
            # 创建栅格矩形并填充蓝色
            from matplotlib.patches import Rectangle
            rect = Rectangle((min_lon, min_lat), grid_size, grid_size, 
                           facecolor='lightblue', edgecolor='blue', 
                           alpha=0.7, linewidth=1)
            ax.add_patch(rect)
            
            # 在栅格中心添加标号
            ax.annotate(f'{i}', (lon, lat), ha='center', va='center',
                       fontsize=10, color='darkblue', fontweight='bold')
    
    # 添加图例（手动创建）
    from matplotlib.patches import Patch
    legend_elements = [
        plt.Line2D([0], [0], color='red', linewidth=2, label='城口流域边界'),
        Patch(facecolor='lightblue', edgecolor='blue', alpha=0.7, 
              label=f'筛选出的栅格 ({len(valid_coords)}个)')
    ]
    
    # 设置坐标轴
    ax.set_xlabel('经度 (Longitude)', fontsize=12)
    ax.set_ylabel('纬度 (Latitude)', fontsize=12)
    ax.set_title('城口流域边界与GFS栅格分布', fontsize=14, fontweight='bold')
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 添加图例
    ax.legend(handles=legend_elements, loc='best', fontsize=10)
    
    # 设置坐标轴范围
    bounds = boundary.total_bounds
    margin = 0.05  # 增加边距
    ax.set_xlim(bounds[0] - margin, bounds[2] + margin)
    ax.set_ylim(bounds[1] - margin, bounds[3] + margin)
    
    # 设置坐标轴刻度格式
    ax.tick_params(axis='both', which='major', labelsize=10)
    
    # 保存图片
    output_file = '/home/<USER>/Flood_flow_prediction/chengkou_boundary_grid_analysis.png'
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"📊 可视化图表已保存到: {output_file}")
    
    plt.show()

def calculate_grid_areas(coordinates):
    """
    计算每个栅格点的面积
    """
    if not coordinates:
        return []
    
    try:
        # 尝试导入面积计算模块
        import sys
        sys.path.append('/home/<USER>/Flood_flow_prediction/data_process')
        from calculate_shange_area import calculate_grid_area
        
        print("\n📐 计算各栅格点面积（GFS数据分辨率约0.25°）...")
        print("-" * 60)
        
        areas = []
        total_area = 0
        
        for i, (lon, lat) in enumerate(coordinates, 1):
            # GFS数据分辨率约为0.25度
            area = calculate_grid_area(lat, lon, grid_size=0.25)
            areas.append(area)
            total_area += area
            print(f"栅格点 {i:2d}: (经度: {lon:8.4f}°, 纬度: {lat:7.4f}°) - 面积: {area:6.2f} 平方公里")
        
        print("-" * 60)
        print(f"总栅格面积: {total_area:.2f} 平方公里")
        print(f"平均栅格面积: {total_area/len(coordinates):.2f} 平方公里")
        
        return areas
        
    except ImportError:
        print("⚠️  无法导入面积计算模块，跳过面积计算")
        return []

if __name__ == "__main__":
    print("=" * 80)
    print("🏞️  城口流域GFS栅格点分析")
    print("=" * 80)
    
    # 分析城口边界内的栅格点
    valid_coords, candidate_coords = analyze_chengkou_grid_points()
    
    if valid_coords:
        print(f"\n✅ 分析完成！")
        print(f"   - 城口流域边界内有效栅格点数量: {len(valid_coords)} 个")
        print(f"   - 检查的候选栅格点总数: {len(candidate_coords)} 个")
        print(f"   - 有效点占比: {len(valid_coords)/len(candidate_coords)*100:.2f}%")
        
        # 计算栅格面积
        areas = calculate_grid_areas(valid_coords)
        
    else:
        print("❌ 未找到有效的栅格点")
    
    print("\n" + "=" * 80)
