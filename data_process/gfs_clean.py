import xarray as xr  # xarray>=2025.1.2 and zarr>=3.0.4 for zarr v3 support
import geopandas as gpd
import pandas as pd
from datetime import datetime
import warnings
from pathlib import Path
from shapely.geometry import Point
warnings.filterwarnings('ignore')

def explore_gfs_data_structure():
    """
    探索GFS数据结构
    """
    print("正在探索GFS数据结构...")
    ds = xr.open_zarr("https://data.dynamical.org/noaa/gfs/forecast/latest.zarr?email=<EMAIL>")
    
    print("数据集维度:")
    for dim, size in ds.dims.items():
        print(f"  {dim}: {size}")
    
    print("\n数据变量:")
    for var in ds.data_vars:
        print(f"  {var}: {ds[var].dims}")
    
    print("\n坐标变量:")
    for coord in ds.coords:
        print(f"  {coord}: {ds[coord].dims}")
    
    # 查看时间相关的坐标
    if 'init_time' in ds.coords:
        print(f"\ninit_time范围: {ds.init_time.min().values} 到 {ds.init_time.max().values}")
    if 'lead_time' in ds.coords:
        print(f"lead_time范围: {ds.lead_time.min().values} 到 {ds.lead_time.max().values}")
    
    return ds

def extract_gfs_precipitation_guojia(start_date="2025-08-01", end_date=None, output_dir="GFS_guojia_data"):
    """
    提取GFS数据中guojia.shp边界范围内的precipitation_surface数据
    
    Parameters:
    -----------
    start_date : str
        开始日期，格式为"YYYY-MM-DD"
    end_date : str, optional
        结束日期，格式为"YYYY-MM-DD"，默认为今天
    output_dir : str
        输出目录
        
    Returns:
    --------
    pd.DataFrame : 处理后的降水数据
    """
    
    # 设置日期范围
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")
    
    print(f"提取时间范围: {start_date} 到 {end_date}")
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 读取guojia.shp边界文件
    print("正在读取guojia流域边界文件...")
    shapefile_path = "/home/<USER>/Flood_flow_prediction/boundary/guojia.shp"
    guojia_boundary = gpd.read_file(shapefile_path)
    
    # 确保坐标系正确
    if guojia_boundary.crs is None:
        guojia_boundary = guojia_boundary.set_crs("EPSG:4326")
    elif guojia_boundary.crs.to_string() != "EPSG:4326":
        guojia_boundary = guojia_boundary.to_crs("EPSG:4326")
    
    # 获取边界框坐标
    bounds = guojia_boundary.total_bounds
    min_lon, min_lat, max_lon, max_lat = bounds
    print(f"guojia流域边界框: 经度 {min_lon:.4f} 到 {max_lon:.4f}, 纬度 {min_lat:.4f} 到 {max_lat:.4f}")
    
    # 加载GFS数据（只加载一次）
    print("正在加载GFS数据...")
    ds = xr.open_zarr("https://data.dynamical.org/noaa/gfs/forecast/latest.zarr?email=<EMAIL>")
    
    # 先进行空间筛选以减少数据量
    print("正在进行空间筛选...")
    buffer = 0.1  # 0.1度缓冲区
    
    # 筛选经纬度范围
    lon_slice = slice(min_lon - buffer, max_lon + buffer)
    lat_slice = slice(max_lat + buffer, min_lat - buffer)  # 注意纬度是从北到南递减的
    
    # 空间裁剪
    ds_spatial = ds.sel(longitude=lon_slice, latitude=lat_slice)
    print(f"空间裁剪后数据大小: 经度 {len(ds_spatial.longitude)} 点, 纬度 {len(ds_spatial.latitude)} 点")
    
    # 检查precipitation_surface变量
    if 'precipitation_surface' not in ds_spatial.data_vars:
        print("错误: 未找到precipitation_surface变量")
        available_vars = list(ds_spatial.data_vars.keys())
        print(f"可用变量: {available_vars}")
        return None
    
    # 存储所有处理后的数据
    all_data = []
    
    # 获取最新的初始化时间（最近的几个）
    latest_init_times = ds_spatial.init_time.values[-10:]  # 取最新的10个初始化时间
    
    print(f"处理最新的 {len(latest_init_times)} 个初始化时间...")
    
    for i, init_time in enumerate(latest_init_times):
        try:
            init_time_dt = pd.to_datetime(init_time)
            print(f"  处理初始化时间 {i+1}/{len(latest_init_times)}: {init_time_dt}")
            
            # 选择该初始化时间的数据
            ds_init = ds_spatial.sel(init_time=init_time)
            
            # 选择1-6小时的预测（跳过0小时预测，因为通常是NaN）
            # 注意：lead_time可能是以小时为单位的timedelta
            lead_times = ds_init.lead_time.values

            print(f"    可用的lead_time: {lead_times[:10]}")  # 显示前10个

            # 筛选1-6小时的预测（跳过第一个0小时）
            if len(lead_times) >= 7:
                selected_lead_times = lead_times[1:7]  # 取第2到第7个时间步（1-6小时）
            elif len(lead_times) >= 2:
                selected_lead_times = lead_times[1:]  # 跳过第一个，取剩余的
            else:
                print(f"    警告: lead_time数量不足，跳过该初始化时间")
                continue

            print(f"    选择的lead_time: {selected_lead_times}")

            ds_forecast = ds_init.sel(lead_time=selected_lead_times)
            
            # 提取precipitation_surface数据
            precip_data = ds_forecast.precipitation_surface

            print(f"    原始数据形状: {precip_data.shape}")
            print(f"    数据范围: {precip_data.min().values} 到 {precip_data.max().values}")

            # 转换为DataFrame
            df = precip_data.to_dataframe().reset_index()
            print(f"    转换为DataFrame后行数: {len(df)}")

            # 检查NaN值
            nan_count = df['precipitation_surface'].isna().sum()
            print(f"    precipitation_surface NaN值数量: {nan_count}")

            # 检查其他列的NaN情况
            print(f"    各列NaN情况:")
            for col in df.columns:
                nan_count_col = df[col].isna().sum()
                if nan_count_col > 0:
                    print(f"      {col}: {nan_count_col}")

            # 只删除precipitation_surface列的NaN值
            df = df.dropna(subset=['precipitation_surface'])
            print(f"    删除precipitation_surface NaN后行数: {len(df)}")

            if len(df) == 0:
                print(f"    警告: 删除precipitation_surface NaN后没有数据")
                continue

            # 重命名列
            if 'precipitation_surface' in df.columns:
                df = df.rename(columns={'precipitation_surface': 'tp'})

            # 计算valid_time（初始化时间 + 预测步长）
            df['valid_time'] = pd.to_datetime(df['init_time']) + pd.to_timedelta(df['lead_time'])
            
            # 使用更宽松的空间筛选策略
            print(f"    正在进行空间筛选...")
            print(f"    数据点数量: {len(df)}")
            print(f"    经度范围: {df['longitude'].min():.4f} 到 {df['longitude'].max():.4f}")
            print(f"    纬度范围: {df['latitude'].min():.4f} 到 {df['latitude'].max():.4f}")

            # 创建点几何体
            geometry = [Point(xy) for xy in zip(df['longitude'], df['latitude'])]
            gdf_points = gpd.GeoDataFrame(df, geometry=geometry, crs="EPSG:4326")

            # 方法1: 先尝试精确的within连接
            points_in_watershed = gpd.sjoin(gdf_points, guojia_boundary, how='inner', predicate='within')

            # 方法2: 如果没有点在流域内，使用intersects（更宽松）
            if len(points_in_watershed) == 0:
                print(f"    没有点严格在流域内，尝试使用intersects...")
                points_in_watershed = gpd.sjoin(gdf_points, guojia_boundary, how='inner', predicate='intersects')

            # 方法3: 如果还是没有，使用距离最近的点
            if len(points_in_watershed) == 0:
                print(f"    没有相交点，使用距离最近的网格点...")
                # 计算流域中心点
                watershed_centroid = guojia_boundary.geometry.centroid.iloc[0]

                # 计算每个网格点到流域中心的距离
                distances = gdf_points.geometry.distance(watershed_centroid)

                # 选择最近的几个点（比如最近的4个点）
                closest_indices = distances.nsmallest(4).index
                points_in_watershed = gdf_points.loc[closest_indices].copy()

                print(f"    选择了最近的 {len(points_in_watershed)} 个网格点")

            if len(points_in_watershed) > 0:
                # 按valid_time分组，对tp进行求和（流域内所有网格点的降水量之和）
                grouped_df = points_in_watershed.groupby('valid_time')['tp'].sum().reset_index()

                # 添加到总数据中
                all_data.append(grouped_df)

                print(f"    成功提取 {len(grouped_df)} 个时间点的数据，使用网格点数: {len(points_in_watershed)}")
            else:
                print(f"    警告: 该初始化时间没有可用的数据点")
                
        except Exception as e:
            print(f"    错误: 处理初始化时间时出错 - {e}")
            continue
    
    # 合并所有数据
    if all_data:
        print(f"\n合并所有数据...")
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # 按时间排序
        combined_df = combined_df.sort_values('valid_time').reset_index(drop=True)
        
        # 去除重复时间点（保留最新的预测）
        # 对于同一个valid_time，保留最后一个（最新的预测）
        combined_df = combined_df.drop_duplicates(subset=['valid_time'], keep='last')
        
        # 重新排序
        combined_df = combined_df.sort_values('valid_time').reset_index(drop=True)
        
        print(f"合并后数据: {len(combined_df)} 条记录")
        print(f"时间范围: {combined_df['valid_time'].min()} 到 {combined_df['valid_time'].max()}")
        
        # 保存到CSV文件
        output_file = output_path / f"guojia_gfs_precipitation_{start_date}_to_{end_date}.csv"
        combined_df.to_csv(output_file, index=False)
        
        print(f"数据已保存到: {output_file}")
        
        # 数据质量检查
        print(f"\n数据质量检查:")
        print(f"  总记录数: {len(combined_df)}")
        print(f"  时间范围: {combined_df['valid_time'].min()} 到 {combined_df['valid_time'].max()}")
        print(f"  降水统计: 最小值={combined_df['tp'].min():.6f}, 最大值={combined_df['tp'].max():.6f}, 平均值={combined_df['tp'].mean():.6f}")
        
        # 检查时间间隔
        combined_df['valid_time'] = pd.to_datetime(combined_df['valid_time'])
        time_diffs = combined_df['valid_time'].diff().dropna()
        unique_intervals = time_diffs.value_counts().sort_index()
        print(f"  时间间隔分布:")
        for interval, count in unique_intervals.items():
            print(f"    {interval}: {count} 次")
        
        return combined_df
    
    else:
        print("未能提取到任何数据")
        return None

if __name__ == "__main__":
    # 首先探索数据结构
    print("🔍 探索GFS数据结构...")
    print("=" * 60)
    
    try:
        ds_structure = explore_gfs_data_structure()
        print("\n" + "=" * 60)
    except Exception as e:
        print(f"探索数据结构时出错: {e}")
        print("继续进行数据提取...")
    
    # 执行数据提取
    print("🌧️  开始提取GFS降水数据...")
    print("=" * 60)
    
    # 提取从2025年1月1日开始至今的数据
    result_df = extract_gfs_precipitation_guojia(
        start_date="2025-01-01",  # 从2025年1月1日开始
        end_date=None,  # 默认到今天
        output_dir="GFS_guojia_data"
    )
    
    if result_df is not None:
        print("\n🎉 数据提取完成！")
        print(f"生成的文件包含 {len(result_df)} 条记录")
        print("数据说明:")
        print("  - valid_time: 预测有效时间")
        print("  - tp: guojia流域内降水量总和")
        print("  - 数据来源: NOAA GFS模式")
        print("  - 时间分辨率: 1小时")
        print("  - 预测策略: 每6小时初始化，取最新6小时预测")
    else:
        print("❌ 数据提取失败！")
