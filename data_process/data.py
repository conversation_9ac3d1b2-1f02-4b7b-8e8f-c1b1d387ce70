import xarray as xr  # xarray>=2025.1.2 and zarr>=3.0.4 for zarr v3 support
import geopandas as gpd
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 加载NOAA GFS数据集
print("正在加载NOAA GFS数据集...")
ds = xr.open_zarr("https://data.dynamical.org/noaa/gfs/forecast/latest.zarr?email=<EMAIL>")

# 读取郭家流域边界
print("正在读取郭家流域边界文件...")
guojia_boundary = gpd.read_file("/home/<USER>/Flood_flow_prediction/boundary/guojia.shp")

# 获取边界框坐标
bounds = guojia_boundary.total_bounds
min_lon, min_lat, max_lon, max_lat = bounds
print(f"郭家流域边界框: 经度 {min_lon:.4f} 到 {max_lon:.4f}, 纬度 {min_lat:.4f} 到 {max_lat:.4f}")

# 设置时间范围：从2025年1月1日开始到今天
start_date = pd.Timestamp('2025-01-01')
end_date = pd.Timestamp('2025-08-05')  # 今天的日期
print(f"提取时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

# 首先查看可用的时间范围
print("数据集中的时间范围:")
print(f"最早时间: {ds.init_time.min().values}")
print(f"最晚时间: {ds.init_time.max().values}")

# 筛选时间范围
print("正在筛选时间范围...")
time_mask = (ds.init_time >= start_date) & (ds.init_time <= end_date)
ds_time_filtered = ds.where(time_mask, drop=True)

print(f"筛选后的时间步数: {len(ds_time_filtered.init_time)}")

# 筛选地理范围（添加缓冲区以确保覆盖完整）
buffer = 0.1  # 0.1度缓冲区
lon_mask = (ds_time_filtered.longitude >= (min_lon - buffer)) & (ds_time_filtered.longitude <= (max_lon + buffer))
lat_mask = (ds_time_filtered.latitude >= (min_lat - buffer)) & (ds_time_filtered.latitude <= (max_lat + buffer))

print("正在筛选地理范围...")
ds_spatial_filtered = ds_time_filtered.where(lon_mask & lat_mask, drop=True)

print(f"筛选后的空间范围: 经度 {len(ds_spatial_filtered.longitude)} 个点, 纬度 {len(ds_spatial_filtered.latitude)} 个点")

# 提取降水数据
print("正在提取precipitation_surface数据...")
precipitation_data = ds_spatial_filtered.precipitation_surface

print("数据提取完成!")
print(f"降水数据形状: {precipitation_data.shape}")
print(f"数据大小: {precipitation_data.nbytes / 1024**2:.2f} MB")

# 显示数据信息
print("\n=== 提取的降水数据信息 ===")
print(precipitation_data)

# 计算一些统计信息
print("\n=== 数据统计信息 ===")
try:
    # 计算平均降水量（转换为mm/hour）
    precip_mean = precipitation_data.mean(['latitude', 'longitude']) * 3600  # mm/s to mm/h
    print(f"平均降水强度: {precip_mean.min().values:.4f} - {precip_mean.max().values:.4f} mm/h")
    
    # 保存数据到NetCDF文件
    output_file = "/home/<USER>/Flood_flow_prediction/data_process/guojia_precipitation_2025.nc"
    print(f"\n正在保存数据到: {output_file}")
    
    # 清理所有坐标和变量的属性
    precip_clean = precipitation_data.copy()
    
    # 清理数据变量的属性
    for var_name in precip_clean.coords:
        if hasattr(precip_clean.coords[var_name], 'attrs'):
            precip_clean.coords[var_name].attrs = {}
    
    # 只保留基本属性
    basic_attrs = {
        'long_name': 'Total Precipitation',
        'short_name': 'tp', 
        'units': 'mm/s',
        'comment': 'Average precipitation rate since the previous forecast step'
    }
    precip_clean.attrs = basic_attrs
    
    precip_clean.to_netcdf(output_file)
    print("数据保存成功!")
    
    # 创建一个简单的时间序列数据（区域平均）
    precip_timeseries = precipitation_data.mean(['latitude', 'longitude'])
    
    # 保存时间序列数据到CSV
    csv_file = "/home/<USER>/Flood_flow_prediction/data_process/guojia_precipitation_timeseries_2025.csv"
    print(f"正在保存时间序列数据到: {csv_file}")
    
    # 转换为DataFrame
    df_list = []
    for i, init_time in enumerate(precip_timeseries.init_time.values):
        for j, lead_time in enumerate(precip_timeseries.lead_time.values):
            valid_time = init_time + lead_time
            precip_value = precip_timeseries.isel(init_time=i, lead_time=j).values * 3600  # 转换为mm/h
            df_list.append({
                'init_time': pd.Timestamp(init_time),
                'lead_time_hours': lead_time / pd.Timedelta('1H'),
                'valid_time': pd.Timestamp(valid_time),
                'precipitation_mm_per_hour': precip_value
            })
    
    df = pd.DataFrame(df_list)
    df.to_csv(csv_file, index=False)
    print("时间序列数据保存成功!")
    print(f"时间序列数据形状: {df.shape}")
    print(f"时间范围: {df['valid_time'].min()} 到 {df['valid_time'].max()}")
    
except Exception as e:
    print(f"处理数据时出错: {e}")

print("\n=== 数据提取完成 ===")