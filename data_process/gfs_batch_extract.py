import xarray as xr
import geopandas as gpd
import pandas as pd
from datetime import datetime, timedelta
import warnings
from pathlib import Path
from shapely.geometry import Point
import time
warnings.filterwarnings('ignore')

def extract_gfs_precipitation_batch(start_date="2025-01-01", end_date=None, output_dir="GFS_guojia_data", batch_days=7):
    """
    分批提取GFS数据中guojia.shp边界范围内的precipitation_surface数据
    
    Parameters:
    -----------
    start_date : str
        开始日期，格式为"YYYY-MM-DD"
    end_date : str, optional
        结束日期，格式为"YYYY-MM-DD"，默认为今天
    output_dir : str
        输出目录
    batch_days : int
        每批处理的天数
        
    Returns:
    --------
    pd.DataFrame : 处理后的降水数据
    """
    
    # 设置日期范围
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")
    
    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")
    
    print(f"提取时间范围: {start_date} 到 {end_date}")
    print(f"分批处理，每批 {batch_days} 天")
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 读取guojia.shp边界文件
    print("正在读取guojia流域边界文件...")
    shapefile_path = "/home/<USER>/Flood_flow_prediction/boundary/guojia.shp"
    guojia_boundary = gpd.read_file(shapefile_path)
    
    # 确保坐标系正确
    if guojia_boundary.crs is None:
        guojia_boundary = guojia_boundary.set_crs("EPSG:4326")
    elif guojia_boundary.crs.to_string() != "EPSG:4326":
        guojia_boundary = guojia_boundary.to_crs("EPSG:4326")
    
    # 获取边界框坐标
    bounds = guojia_boundary.total_bounds
    min_lon, min_lat, max_lon, max_lat = bounds
    print(f"guojia流域边界框: 经度 {min_lon:.4f} 到 {max_lon:.4f}, 纬度 {min_lat:.4f} 到 {max_lat:.4f}")
    
    # 存储所有批次的数据
    all_batch_data = []
    
    # 分批处理
    current_date = start_dt
    batch_num = 1
    
    while current_date <= end_dt:
        batch_end_date = min(current_date + timedelta(days=batch_days-1), end_dt)
        
        print(f"\n{'='*60}")
        print(f"处理批次 {batch_num}: {current_date.strftime('%Y-%m-%d')} 到 {batch_end_date.strftime('%Y-%m-%d')}")
        print(f"{'='*60}")
        
        try:
            # 加载GFS数据（每批只加载一次）
            print("正在加载GFS数据...")
            ds = xr.open_zarr("https://data.dynamical.org/noaa/gfs/forecast/latest.zarr?email=<EMAIL>")
            
            # 先进行空间筛选以减少数据量
            print("正在进行空间筛选...")
            buffer = 0.1  # 0.1度缓冲区
            
            # 筛选经纬度范围
            lon_slice = slice(min_lon - buffer, max_lon + buffer)
            lat_slice = slice(max_lat + buffer, min_lat - buffer)  # 注意纬度是从北到南递减的
            
            # 空间裁剪
            ds_spatial = ds.sel(longitude=lon_slice, longitude=lat_slice)
            print(f"空间裁剪后数据大小: 经度 {len(ds_spatial.longitude)} 点, 纬度 {len(ds_spatial.latitude)} 点")
            
            # 检查precipitation_surface变量
            if 'precipitation_surface' not in ds_spatial.data_vars:
                print("错误: 未找到precipitation_surface变量")
                continue
            
            # 筛选时间范围内的初始化时间
            batch_start_time = pd.Timestamp(current_date)
            batch_end_time = pd.Timestamp(batch_end_date) + pd.Timedelta(days=1)
            
            # 获取该批次时间范围内的初始化时间
            init_times = ds_spatial.init_time.values
            init_times_pd = pd.to_datetime(init_times)
            
            # 筛选批次时间范围内的初始化时间
            mask = (init_times_pd >= batch_start_time) & (init_times_pd < batch_end_time)
            batch_init_times = init_times[mask]
            
            if len(batch_init_times) == 0:
                print(f"该批次没有可用的初始化时间，跳过")
                current_date = batch_end_date + timedelta(days=1)
                batch_num += 1
                continue
            
            print(f"该批次有 {len(batch_init_times)} 个初始化时间")
            
            # 存储该批次的数据
            batch_data = []
            
            # 处理该批次的每个初始化时间
            for i, init_time in enumerate(batch_init_times):
                try:
                    init_time_dt = pd.to_datetime(init_time)
                    print(f"  处理初始化时间 {i+1}/{len(batch_init_times)}: {init_time_dt}")
                    
                    # 选择该初始化时间的数据
                    ds_init = ds_spatial.sel(init_time=init_time)
                    
                    # 选择1-6小时的预测（跳过0小时预测）
                    lead_times = ds_init.lead_time.values
                    
                    # 筛选1-6小时的预测
                    if len(lead_times) >= 7:
                        selected_lead_times = lead_times[1:7]  # 取第2到第7个时间步（1-6小时）
                    elif len(lead_times) >= 2:
                        selected_lead_times = lead_times[1:]  # 跳过第一个，取剩余的
                    else:
                        print(f"    警告: lead_time数量不足，跳过该初始化时间")
                        continue
                    
                    ds_forecast = ds_init.sel(lead_time=selected_lead_times)
                    
                    # 提取precipitation_surface数据
                    precip_data = ds_forecast.precipitation_surface
                    
                    # 转换为DataFrame
                    df = precip_data.to_dataframe().reset_index()
                    
                    # 只删除precipitation_surface列的NaN值
                    df = df.dropna(subset=['precipitation_surface'])
                    
                    if len(df) == 0:
                        print(f"    警告: 该初始化时间没有有效数据")
                        continue
                    
                    # 重命名列
                    if 'precipitation_surface' in df.columns:
                        df = df.rename(columns={'precipitation_surface': 'tp'})
                    
                    # 计算valid_time（初始化时间 + 预测步长）
                    df['valid_time'] = pd.to_datetime(df['init_time']) + pd.to_timedelta(df['lead_time'])
                    
                    # 使用距离最近的网格点策略（简化处理）
                    # 计算流域中心点
                    watershed_centroid = guojia_boundary.geometry.centroid.iloc[0]
                    
                    # 创建点几何体
                    geometry = [Point(xy) for xy in zip(df['longitude'], df['latitude'])]
                    gdf_points = gpd.GeoDataFrame(df, geometry=geometry, crs="EPSG:4326")
                    
                    # 计算每个网格点到流域中心的距离
                    distances = gdf_points.geometry.distance(watershed_centroid)
                    
                    # 选择最近的几个点（比如最近的4个点）
                    closest_indices = distances.nsmallest(4).index
                    points_selected = gdf_points.loc[closest_indices].copy()
                    
                    if len(points_selected) > 0:
                        # 按valid_time分组，对tp进行求和
                        grouped_df = points_selected.groupby('valid_time')['tp'].sum().reset_index()
                        
                        # 添加到批次数据中
                        batch_data.append(grouped_df)
                        
                        print(f"    成功提取 {len(grouped_df)} 个时间点的数据")
                    
                except Exception as e:
                    print(f"    错误: 处理初始化时间时出错 - {e}")
                    continue
            
            # 合并该批次的数据
            if batch_data:
                print(f"\n合并批次 {batch_num} 的数据...")
                batch_combined = pd.concat(batch_data, ignore_index=True)
                
                # 按时间排序并去重
                batch_combined = batch_combined.sort_values('valid_time').reset_index(drop=True)
                batch_combined = batch_combined.drop_duplicates(subset=['valid_time'], keep='last')
                
                # 添加到总数据中
                all_batch_data.append(batch_combined)
                
                print(f"批次 {batch_num} 完成: {len(batch_combined)} 条记录")
                print(f"时间范围: {batch_combined['valid_time'].min()} 到 {batch_combined['valid_time'].max()}")
            else:
                print(f"批次 {batch_num} 没有提取到数据")
            
        except Exception as e:
            print(f"批次 {batch_num} 处理失败: {e}")
        
        # 移动到下一批次
        current_date = batch_end_date + timedelta(days=1)
        batch_num += 1
        
        # 添加延迟以避免过度请求
        time.sleep(2)
    
    # 合并所有批次的数据
    if all_batch_data:
        print(f"\n{'='*60}")
        print(f"合并所有批次数据...")
        final_combined = pd.concat(all_batch_data, ignore_index=True)
        
        # 最终排序和去重
        final_combined = final_combined.sort_values('valid_time').reset_index(drop=True)
        final_combined = final_combined.drop_duplicates(subset=['valid_time'], keep='last')
        
        print(f"最终合并数据: {len(final_combined)} 条记录")
        print(f"时间范围: {final_combined['valid_time'].min()} 到 {final_combined['valid_time'].max()}")
        
        # 保存到CSV文件
        output_file = output_path / f"guojia_gfs_precipitation_{start_date}_to_{end_date}_complete.csv"
        final_combined.to_csv(output_file, index=False)
        
        print(f"数据已保存到: {output_file}")
        
        # 数据质量检查
        print(f"\n数据质量检查:")
        print(f"  总记录数: {len(final_combined)}")
        print(f"  时间范围: {final_combined['valid_time'].min()} 到 {final_combined['valid_time'].max()}")
        print(f"  降水统计: 最小值={final_combined['tp'].min():.6f}, 最大值={final_combined['tp'].max():.6f}, 平均值={final_combined['tp'].mean():.6f}")
        
        return final_combined
    
    else:
        print("未能提取到任何数据")
        return None

if __name__ == "__main__":
    print("🌧️  开始分批提取GFS降水数据...")
    print("=" * 60)
    
    # 先测试提取最近一个月的数据
    result_df = extract_gfs_precipitation_batch(
        start_date="2025-07-01",  # 从7月1日开始测试
        end_date=None,  # 默认到今天
        output_dir="GFS_guojia_data",
        batch_days=3  # 每批处理3天，减少数据量
    )
    
    if result_df is not None:
        print("\n🎉 数据提取完成！")
        print(f"生成的文件包含 {len(result_df)} 条记录")
        print("数据说明:")
        print("  - valid_time: 预测有效时间")
        print("  - tp: guojia流域内降水量总和")
        print("  - 数据来源: NOAA GFS模式")
        print("  - 时间分辨率: 1小时")
        print("  - 预测策略: 每6小时初始化，取最新6小时预测")
        print("  - 处理方式: 分批处理，选择距离流域中心最近的4个网格点")
    else:
        print("❌ 数据提取失败！")
