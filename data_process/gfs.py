import xarray as xr  # xarray>=2025.1.2 and zarr>=3.0.4 for zarr v3 support
import geopandas as gpd
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
from pathlib import Path
from shapely.geometry import mapping
import rioxarray as rxr
warnings.filterwarnings('ignore')

def extract_gfs_precipitation_guojia(start_date="2025-01-01", end_date=None, output_dir="GFS_guojia_data"):
    """
    提取GFS数据中guojia.shp边界范围内的precipitation_surface数据

    Parameters:
    -----------
    start_date : str
        开始日期，格式为"YYYY-MM-DD"
    end_date : str, optional
        结束日期，格式为"YYYY-MM-DD"，默认为今天
    output_dir : str
        输出目录

    Returns:
    --------
    pd.DataFrame : 处理后的降水数据
    """

    # 设置日期范围
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")

    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")

    print(f"提取时间范围: {start_date} 到 {end_date}")

    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)

    # 读取guojia.shp边界文件
    print("正在读取guojia流域边界文件...")
    shapefile_path = "/home/<USER>/Flood_flow_prediction/boundary/guojia.shp"
    guojia_boundary = gpd.read_file(shapefile_path)

    # 确保坐标系正确
    if guojia_boundary.crs is None:
        guojia_boundary = guojia_boundary.set_crs("EPSG:4326")
    elif guojia_boundary.crs.to_string() != "EPSG:4326":
        guojia_boundary = guojia_boundary.to_crs("EPSG:4326")

    # 获取边界框坐标
    bounds = guojia_boundary.total_bounds
    min_lon, min_lat, max_lon, max_lat = bounds
    print(f"guojia流域边界框: 经度 {min_lon:.4f} 到 {max_lon:.4f}, 纬度 {min_lat:.4f} 到 {max_lat:.4f}")

    # 存储所有处理后的数据
    all_data = []

    # GFS每6小时初始化一次：00, 06, 12, 18 UTC
    initialization_hours = [0, 6, 12, 18]

    # 处理每一天
    current_date = start_dt
    while current_date <= end_dt:
        print(f"\n处理日期: {current_date.strftime('%Y-%m-%d')}")

        # 处理每个初始化时间
        for init_hour in initialization_hours:
            try:
                # 构建初始化时间
                init_time = current_date.replace(hour=init_hour, minute=0, second=0, microsecond=0)

                print(f"  处理初始化时间: {init_time.strftime('%Y-%m-%d %H:%M')} UTC")

                # 加载GFS数据
                ds = xr.open_zarr("https://data.dynamical.org/noaa/gfs/forecast/latest.zarr?email=<EMAIL>")

                # 筛选时间范围 - 获取该初始化时间的最新6小时预测
                # GFS提供0-384小时的预测，我们只需要前6小时
                forecast_hours = [1, 2, 3, 4, 5, 6]  # 1-6小时预测

                # 筛选初始化时间
                ds_init = ds.sel(time=init_time, method='nearest')

                # 筛选预测步长（前6小时）
                forecast_steps = [pd.Timedelta(hours=h) for h in forecast_hours]
                ds_forecast = ds_init.sel(step=forecast_steps, method='nearest')

                # 筛选地理范围（添加缓冲区）
                buffer = 0.1  # 0.1度缓冲区
                lon_mask = (ds_forecast.longitude >= (min_lon - buffer)) & (ds_forecast.longitude <= (max_lon + buffer))
                lat_mask = (ds_forecast.latitude >= (min_lat - buffer)) & (ds_forecast.latitude <= (max_lat + buffer))

                ds_spatial = ds_forecast.where(lon_mask & lat_mask, drop=True)

                # 提取precipitation_surface数据
                if 'precipitation_surface' in ds_spatial.data_vars:
                    precip_data = ds_spatial.precipitation_surface

                    # 设置空间参考系统
                    precip_data = precip_data.rio.write_crs("EPSG:4326")

                    # 使用shapefile裁剪数据
                    clipped_data = precip_data.rio.clip(guojia_boundary.geometry.apply(mapping),
                                                       guojia_boundary.crs, drop=False)

                    # 转换为DataFrame
                    df = clipped_data.to_dataframe().reset_index()
                    df = df.dropna()

                    # 重命名列
                    if 'precipitation_surface' in df.columns:
                        df = df.rename(columns={'precipitation_surface': 'tp'})

                    # 计算valid_time（初始化时间 + 预测步长）
                    df['valid_time'] = df['time'] + df['step']

                    # 按valid_time分组，对tp进行求和（流域内所有网格点的降水量之和）
                    grouped_df = df.groupby('valid_time')['tp'].sum().reset_index()

                    # 添加到总数据中
                    all_data.append(grouped_df)

                    print(f"    成功提取 {len(grouped_df)} 个时间点的数据")

                else:
                    print(f"    警告: 未找到precipitation_surface变量")

            except Exception as e:
                print(f"    错误: 处理初始化时间 {init_hour:02d}:00 时出错 - {e}")
                continue

        # 移动到下一天
        current_date += timedelta(days=1)

    # 合并所有数据
    if all_data:
        print(f"\n合并所有数据...")
        combined_df = pd.concat(all_data, ignore_index=True)

        # 按时间排序
        combined_df = combined_df.sort_values('valid_time').reset_index(drop=True)

        # 去除重复时间点（保留最新的预测）
        # 对于同一个valid_time，保留最后一个（最新的预测）
        combined_df = combined_df.drop_duplicates(subset=['valid_time'], keep='last')

        # 重新排序
        combined_df = combined_df.sort_values('valid_time').reset_index(drop=True)

        print(f"合并后数据: {len(combined_df)} 条记录")
        print(f"时间范围: {combined_df['valid_time'].min()} 到 {combined_df['valid_time'].max()}")

        # 保存到CSV文件
        output_file = output_path / f"guojia_gfs_precipitation_{start_date}_to_{end_date}.csv"
        combined_df.to_csv(output_file, index=False)

        print(f"数据已保存到: {output_file}")

        # 数据质量检查
        print(f"\n数据质量检查:")
        print(f"  总记录数: {len(combined_df)}")
        print(f"  时间范围: {combined_df['valid_time'].min()} 到 {combined_df['valid_time'].max()}")
        print(f"  降水统计: 最小值={combined_df['tp'].min():.6f}, 最大值={combined_df['tp'].max():.6f}, 平均值={combined_df['tp'].mean():.6f}")

        # 检查时间间隔
        combined_df['valid_time'] = pd.to_datetime(combined_df['valid_time'])
        time_diffs = combined_df['valid_time'].diff().dropna()
        unique_intervals = time_diffs.value_counts().sort_index()
        print(f"  时间间隔分布:")
        for interval, count in unique_intervals.items():
            print(f"    {interval}: {count} 次")

        return combined_df

    else:
        print("未能提取到任何数据")
        return None

if __name__ == "__main__":
    # 执行数据提取
    print("🌧️  开始提取GFS降水数据...")
    print("=" * 60)

    # 提取从2025年1月1日开始至今的数据
    result_df = extract_gfs_precipitation_guojia(
        start_date="2025-01-01",
        end_date=None,  # 默认到今天
        output_dir="GFS_guojia_data"
    )

    if result_df is not None:
        print("\n🎉 数据提取完成！")
        print(f"生成的文件包含 {len(result_df)} 条记录")
        print("数据说明:")
        print("  - valid_time: 预测有效时间")
        print("  - tp: guojia流域内降水量总和")
        print("  - 数据来源: NOAA GFS模式")
        print("  - 时间分辨率: 1小时")
        print("  - 预测策略: 每6小时初始化，取最新6小时预测")
    else:
        print("❌ 数据提取失败！")